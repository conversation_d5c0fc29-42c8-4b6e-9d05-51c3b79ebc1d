'use client';

import { ReactNode } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Trash2, ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAppStore } from "@/app/store";
import { useDeleteConfirm } from "../../hooks/useDeleteConfirm";

interface TagItemWrapperProps {
  children: ReactNode;
  arrayPath: string;
  index: number;
  totalItems: number;
  className?: string;
}

/**
 * A wrapper for inline, tag-like items in an array (e.g., skills, technologies).
 * It provides hover controls (move up, move down, delete) that appear to the right
 * of the content, ensuring the tag's text is never obscured.
 * This follows a cleaner, Apple-style interaction pattern for small elements.
 */
export function TagItemWrapper({
  children,
  arrayPath,
  index,
  totalItems,
  className,
}: TagItemWrapperProps) {
  const { deleteArrayItem, moveArrayItem } = useAppStore();
  const { confirmDelete } = useDeleteConfirm();

  const canMoveUp = index > 0;
  const canMoveDown = index < totalItems - 1;

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    const confirmed = await confirmDelete({
      title: t("deleteTitle"),
      description: t("deleteDescription"),
    });
    if (confirmed) {
      deleteArrayItem(arrayPath, index);
    }
  };

  const handleMoveUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    moveArrayItem(arrayPath, index, index - 1);
  };

  const handleMoveDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    moveArrayItem(arrayPath, index, index + 1);
  };

  return (
    <div className={cn("relative group/tag inline-flex items-center", className)}>
      {children}
      {/* 
        NEW INTERACTION MODEL:
        - The button container is now part of the flex layout.
        - By default, it has zero width and is invisible (w-0, opacity-0).
        - On hover, it smoothly transitions to its natural width (w-auto) and becomes visible.
        - This physically pushes adjacent elements aside, creating space for the controls.
        - This approach is more robust than z-index and avoids all overlapping issues.
      */}
      <div
        className={cn(
          "flex items-center gap-0.5 no-print transition-all duration-200 ease-in-out overflow-hidden",
          "w-0 opacity-0 group-hover/tag:w-auto group-hover/tag:opacity-100 group-hover/tag:ml-1"
        )}
      >
        {canMoveUp && (
          <Button
            size="icon"
            variant="ghost"
            className="h-5 w-5 p-0 bg-background/80 hover:bg-muted shadow-sm rounded-full flex-shrink-0"
            onClick={handleMoveUp}
          >
            <ChevronUp className="h-3 w-3" />
          </Button>
        )}
        {canMoveDown && (
          <Button
            size="icon"
            variant="ghost"
            className="h-5 w-5 p-0 bg-background/80 hover:bg-muted shadow-sm rounded-full flex-shrink-0"
            onClick={handleMoveDown}
          >
            <ChevronDown className="h-3 w-3" />
          </Button>
        )}
        <Button
          size="icon"
          variant="ghost"
          className="h-5 w-5 p-0 bg-background/80 hover:bg-destructive/10 shadow-sm rounded-full flex-shrink-0"
          onClick={handleDelete}
        >
          <Trash2 className="h-3 w-3 text-destructive/80" />
        </Button>
      </div>
    </div>
  );
}
