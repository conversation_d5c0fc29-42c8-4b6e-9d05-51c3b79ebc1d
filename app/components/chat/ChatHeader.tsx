"use client";

import { useTranslations } from "next-intl";
import { MessageCircleCode, Crown, AlertCircle } from "lucide-react";
import { useSession } from "next-auth/react";
import { useMembership, useMembershipDialog } from "@/app/hooks/useMembership";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MembershipDialog } from "@/app/components/membership/membership-dialog";

export const ChatHeader = () => {
  const t = useTranslations("ChatArea");
  const { data: session } = useSession();
  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const {
    isOpen: isMembershipDialogOpen,
    openMembershipDialog,
    closeMembershipDialog,
  } = useMembershipDialog();

  return (
    <div className="px-4 py-4.5 border-b">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <MessageCircleCode className="mr-2 h-5 w-5" />
          <h2 className="text-lg font-semibold">{t("title")}</h2>
        </div>

        {session?.user && membershipStatus && (
          <div className="flex items-center gap-2">
            {membershipStatus.isMember ? (
              <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                <Crown className="w-3 h-3" />
                <span>会员</span>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <span>{membershipStatus.remainingChats}/5</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={openMembershipDialog}
                  className="h-6 px-2 text-xs"
                >
                  升级
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
