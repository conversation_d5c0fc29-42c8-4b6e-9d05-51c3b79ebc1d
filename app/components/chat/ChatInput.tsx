
"use client";

import { useState, KeyboardEvent } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { SendHorizontal } from "lucide-react";
import { useTranslations } from "next-intl";

interface ChatInputProps {
  isGenerating: boolean;
  onSendMessage: (message: string) => void;
}

export const ChatInput = ({ isGenerating, onSendMessage }: ChatInputProps) => {
  const t = useTranslations("ChatArea");
  const [input, setInput] = useState("");

  const handleKeyDown = (event: KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  };

  const handleSubmit = () => {
    if (input.trim() !== "") {
      onSendMessage(input);
      setInput("");
    }
  };

  return (
    <div className="p-4 pt-2 flex flex-col items-start gap-2 border-t">
      <div className="relative w-full">
        <Textarea
          placeholder={t("placeholder")}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isGenerating}
          rows={2}
          className="resize-none pr-12 focus-visible:ring-1"
        />
        <Button
          type="submit"
          size="icon"
          className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full w-8 h-8"
          onClick={handleSubmit}
          disabled={isGenerating || !input.trim()}
        >
          <SendHorizontal className="w-4 h-4" />
        </Button>
      </div>
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2"></div>
        <p className="text-xs text-muted-foreground">{t("enterToSend")}</p>
      </div>
    </div>
  );
};
