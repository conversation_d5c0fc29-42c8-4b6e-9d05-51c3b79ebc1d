"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import { Languages, Settings, User, LogOut, Crown } from "lucide-react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { useAppStore } from "../store";
import { LoginDialog } from "./auth/login-dialog";
import { useLogoutConfirm } from "../hooks/useLogoutConfirm";
import { useEffect } from "react";
import { useMembership, useMembershipDialog } from "../hooks/useMembership";
import { MembershipDialog } from "./membership/membership-dialog";

export function AppHeader() {
  const t = useTranslations("AppHeader");
  const t_chat = useTranslations("ChatHeader");
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const { data: session } = useSession();
  const { user, setUser, isLoginDialogOpen, setLoginDialogOpen } =
    useAppStore();
  const { confirmLogout } = useLogoutConfirm();
  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const {
    isOpen: isMembershipDialogOpen,
    openMembershipDialog,
    closeMembershipDialog,
  } = useMembershipDialog();

  // Sync session with store
  useEffect(() => {
    if (session?.user) {
      setUser({
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        phone: session.user.phone,
        image: session.user.image,
      });
    } else {
      setUser(null);
    }
  }, [session, setUser]);

  const changeLocale = (newLocale: string) => {
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.replace(newPath);
  };

  const handleUserButtonClick = () => {
    if (!user) {
      setLoginDialogOpen(true);
    }
  };

  const handleLogoutClick = async () => {
    const confirmed = await confirmLogout();

    if (confirmed) {
      await signOut({ callbackUrl: "/" });
    }
  };

  const handlePaymentSuccess = () => {
    refreshMembershipStatus();
  };

  return (
    <header className="flex items-center justify-between p-3 border-b no-print">
      <MembershipDialog
        open={isMembershipDialogOpen}
        onOpenChange={closeMembershipDialog}
        onPaymentSuccess={handlePaymentSuccess}
        currentChatCount={membershipStatus?.chatCount}
        remainingChats={membershipStatus?.remainingChats}
      />
      <div className="flex items-center gap-2">
        <Image
          src="/firsume-logo.svg"
          alt="Firsume Logo"
          width={24}
          height={24}
          className="w-6 h-6"
        />
        <h1 className="text-xl font-bold">Firsume</h1>
      </div>
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={openMembershipDialog}>
          <Crown className="w-3 h-3" />
          {t_chat("premium")}
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Languages className="h-[1.2rem] w-[1.2rem]" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => changeLocale("en")}>
              English
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => changeLocale("zh")}>
              中文
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => changeLocale("fr")}>
              Français
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => changeLocale("de")}>
              Deutsch
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => changeLocale("ja")}>
              日本語
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => changeLocale("ko")}>
              한국어
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        {user ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {user.image ? (
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={user.image} alt={user.name || ""} />
                    <AvatarFallback>
                      {user.name?.charAt(0) || user.email?.charAt(0) || "U"}
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <User className="h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <User className="h-4 w-4 mr-2" />
                {t("profile")}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                {t("settings")}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogoutClick}>
                <LogOut className="h-4 w-4 mr-2" />
                {t("logout")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <Button variant="outline" size="sm" onClick={handleUserButtonClick}>
            <User className="h-4 w-4" />
          </Button>
        )}
      </div>

      <LoginDialog open={isLoginDialogOpen} onOpenChange={setLoginDialogOpen} />
    </header>
  );
}
