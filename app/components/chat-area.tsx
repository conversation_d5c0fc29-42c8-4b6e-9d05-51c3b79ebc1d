"use client";

import { useAppStore } from "@/app/store";
import { useEffect } from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { useSession } from "next-auth/react";
import { useMembership, useMembershipDialog } from "@/app/hooks/useMembership";
import { ChatHeader } from "./chat/ChatHeader";
import { MessageList } from "./chat/MessageList";
import { ChatInput } from "./chat/ChatInput";
import { MembershipDialog } from "./membership/membership-dialog";

export function ChatArea() {
  const t = useTranslations("ChatArea");
  const locale = useLocale();
  const { data: session } = useSession();
  const {
    addMessage,
    resumeData,
    setResumeData,
    setIsGenerating,
    setLoginDialogOpen,
    isDefaultResumeData,
  } = useAppStore();

  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const {
    isOpen: isMembershipDialogOpen,
    openMembershipDialog,
    closeMembershipDialog,
  } = useMembershipDialog();

  useEffect(() => {
    const { messages } = useAppStore.getState();
    if (messages.length === 0) {
      addMessage({ role: "assistant", content: t("initialMessage") });
    }
  }, [addMessage, t]);

  const handlePaymentSuccess = () => {
    refreshMembershipStatus();
  };

  const handleSendMessage = async (userInput: string) => {
    if (!session?.user) {
      setLoginDialogOpen(true);
      return;
    }

    if (membershipStatus && !membershipStatus.canChat) {
      openMembershipDialog();
      return;
    }

    addMessage({ role: "user", content: userInput });
    setIsGenerating(true);

    const requestResumeData = isDefaultResumeData() ? null : resumeData;

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userInput,
          resumeData: requestResumeData,
          locale,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.membershipRequired) {
          openMembershipDialog();
          return;
        }
        throw new Error("API request failed");
      }

      const { resumeData: updatedResume, message: assistantMessage } =
        await response.json();

      setResumeData(updatedResume);
      addMessage({ role: "assistant", content: assistantMessage });

      refreshMembershipStatus();
    } catch (error) {
      console.error("Failed to generate or edit resume:", error);
      addMessage({ role: "assistant", content: t("errorMessage") });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <ChatHeader />
      <MessageList />
      <ChatInput
        isGenerating={useAppStore((s) => s.isGenerating)}
        onSendMessage={handleSendMessage}
      />
      <MembershipDialog
        open={isMembershipDialogOpen}
        onOpenChange={closeMembershipDialog}
        onPaymentSuccess={handlePaymentSuccess}
        currentChatCount={membershipStatus?.chatCount}
        remainingChats={membershipStatus?.remainingChats}
      />
    </div>
  );
}
