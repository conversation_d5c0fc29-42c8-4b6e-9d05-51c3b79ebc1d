import { AppHeader } from "../components/app-header";
import { ChatArea } from "../components/chat-area";
import { ResumePreview } from "../components/resume-preview";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";

export default function Home() {
  return (
    <div className="h-screen flex flex-col">
      <AppHeader />
      <main className="flex-grow overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          <ResizablePanel defaultSize={35} minSize={25}>
            <ChatArea />
          </ResizablePanel>
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={65} minSize={40}>
            <ResumePreview />
          </ResizablePanel>
        </ResizablePanelGroup>
      </main>
    </div>
  );
}
