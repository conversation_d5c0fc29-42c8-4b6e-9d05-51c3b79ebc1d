// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  phone         String?   @unique
  emailVerified DateTime?
  phoneVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 会员相关字段
  chatCount     Int       @default(0)    // 已使用的聊天次数
  isMember      Boolean   @default(false) // 是否为会员
  memberExpiry  DateTime? // 会员到期时间

  accounts      Account[]
  sessions      Session[]
  orders        Order[]   // 订单关系
  resumes       Resume[]  // 简历关系
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// SMS verification codes
model SmsVerification {
  id        String   @id @default(cuid())
  phone     String
  code      String
  expires   DateTime
  verified  Boolean  @default(false)
  createdAt DateTime @default(now())

  @@unique([phone, code])
}

// 商品模型
model Product {
  id          String      @id @default(cuid())
  name        String      // 商品名称
  description String?     // 商品描述
  price       Decimal     @db.Decimal(10, 2) // 商品价格
  duration    Int         // 权益时长（天数）
  type        ProductType @default(MEMBERSHIP) // 商品类型
  isActive    Boolean     @default(true)      // 是否启用
  sortOrder   Int         @default(0)         // 排序顺序
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  orders      Order[]     // 订单关系
}

// 订单模型
model Order {
  id          String      @id @default(cuid())
  userId      String
  productId   String      // 关联商品
  amount      Decimal     @db.Decimal(10, 2) // 订单金额
  status      OrderStatus @default(PENDING)  // 订单状态
  duration    Int         // 会员天数（从商品复制）
  tradeNo     String?     @unique             // 支付宝交易号
  outTradeNo  String      @unique             // 商户订单号
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  paidAt      DateTime?   // 支付时间

  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  product     Product     @relation(fields: [productId], references: [id], onDelete: Restrict)
  payments    Payment[]   // 支付记录
}

// 支付记录模型
model Payment {
  id          String        @id @default(cuid())
  orderId     String
  amount      Decimal       @db.Decimal(10, 2)
  method      PaymentMethod @default(ALIPAY)
  status      PaymentStatus @default(PENDING)
  tradeNo     String?       // 支付平台交易号
  rawData     Json?         // 支付平台返回的原始数据
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  order       Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

// 订单状态枚举
enum OrderStatus {
  PENDING   // 待支付
  PAID      // 已支付
  EXPIRED   // 已过期
  CANCELLED // 已取消
}

// 商品类型枚举
enum ProductType {
  MEMBERSHIP // 会员订阅
  VIP        // VIP会员
  PREMIUM    // 高级会员
}

// 订单类型枚举
enum OrderType {
  MEMBERSHIP // 会员订阅
}

// 支付方式枚举
enum PaymentMethod {
  ALIPAY    // 支付宝
  WECHAT    // 微信支付
  STRIPE    // Stripe支付
}

// 支付状态枚举
enum PaymentStatus {
  PENDING   // 待支付
  SUCCESS   // 支付成功
  FAILED    // 支付失败
  CANCELLED // 已取消
  REFUNDED  // 已退款
}

// 简历模型
model Resume {
  id          String   @id @default(cuid())
  userId      String
  name        String   // 简历名称
  resumeData  Json     // 简历数据（JSON格式存储）
  isDefault   Boolean  @default(false) // 是否为默认简历
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([userId, isDefault])
}
